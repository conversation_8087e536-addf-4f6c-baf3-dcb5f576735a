version: "3.8"

services:
  mongo1:
    image: mongo:latest
    container_name: mongo1
    restart: always
    ports:
      - "27017:27017"
    command: [ "mongod", "--replSet", "rs0", "--bind_ip_all", "--noauth" ]
    volumes:
      - mongo1_data:/data/db

  mongo2:
    image: mongo:latest
    container_name: mongo2
    restart: always
    ports:
      - "27018:27017"
    command: [ "mongod", "--replSet", "rs0", "--bind_ip_all", "--noauth" ]
    volumes:
      - mongo2_data:/data/db

  mongo3:
    image: mongo:latest
    container_name: mongo3
    restart: always
    ports:
      - "27019:27017"
    command: [ "mongod", "--replSet", "rs0", "--bind_ip_all", "--noauth" ]
    volumes:
      - mongo3_data:/data/db

  mongo-init:
    image: mongo:latest
    container_name: mongo-init
    depends_on:
      - mongo1
      - mongo2
      - mongo3
    entrypoint: >
      bash -c "
      sleep 10 &&
      mongosh --host localhost:27017 --eval '
        rs.initiate({
          _id: \"rs0\",
          members: [
            { _id: 0, host: \"localhost:27017\" },
            { _id: 1, host: \"localhost:27018\" },
            { _id: 2, host: \"localhost:27019\" }
          ]
        })
      '
      "
volumes:
  mongo1_data:
  mongo2_data:
  mongo3_data: